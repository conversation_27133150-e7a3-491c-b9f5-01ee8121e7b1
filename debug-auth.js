// Debug utility để check auth status trong browser
// Paste vào browser console để test

async function checkAuthStatus() {
  console.group('🔍 Auth Status Debug');
  
  // 1. Check cookies
  console.log('📝 Current cookies:', document.cookie);
  
  // 2. Check session endpoint
  try {
    const sessionResponse = await fetch('/api/auth/session');
    const sessionData = await sessionResponse.json();
    console.log('✅ Session API response:', sessionData);
  } catch (error) {
    console.error('❌ Session API error:', error);
  }
  
  // 3. Force create guest session
  console.log('🔄 Creating guest session...');
  try {
    const guestResponse = await fetch('/api/auth/guest');
    console.log('✅ Guest auth response:', guestResponse.status, guestResponse.headers.get('set-cookie'));
    
    // Check session again after guest creation
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newSessionResponse = await fetch('/api/auth/session');
    const newSessionData = await newSessionResponse.json();
    console.log('✅ New session after guest:', newSessionData);
    
  } catch (error) {
    console.error('❌ Guest auth error:', error);
  }
  
  // 4. Test chat API with current session
  console.log('🚀 Testing chat API...');
  try {
    const chatResponse = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: crypto.randomUUID(),
        message: {
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
          role: 'user',
          content: 'Test message',
          parts: [{ type: 'text', text: 'Test message' }]
        },
        selectedChatModel: 'chat-model',
        selectedVisibilityType: 'private'
      })
    });
    
    console.log('✅ Chat API response status:', chatResponse.status);
    console.log('✅ Chat API headers:', Object.fromEntries(chatResponse.headers.entries()));
    
    if (chatResponse.ok) {
      console.log('🎉 Chat API working! Streaming response received.');
    } else {
      const errorText = await chatResponse.text();
      console.error('❌ Chat API error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Chat API test error:', error);
  }
  
  console.groupEnd();
}

// Usage: 
// checkAuthStatus()
console.log('🔧 Auth debug utility loaded. Run: checkAuthStatus()');
