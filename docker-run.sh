#!/usr/bin/env bash
set -e

echo "🐳 CS Agent Docker Runner"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if .env exists
if [[ ! -f ".env" ]]; then
    print_warning ".env file not found. Please create it from .env.example"
    exit 1
fi

# Change to docker directory and run
cd docker

print_status "Starting CS Agent with Docker Compose..."
docker-compose up -d

print_status "✅ CS Agent is running!"
echo ""
print_status "Access the application at:"
echo "  Web Interface: http://localhost:3000"
echo "  PathRAG API: http://localhost:8123"
echo ""
print_status "To view logs: cd docker && docker-compose logs -f"
print_status "To stop: cd docker && docker-compose down"
