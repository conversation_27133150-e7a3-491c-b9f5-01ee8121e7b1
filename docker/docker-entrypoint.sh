#!/usr/bin/env bash
set -e

echo "🐳 Starting CS Agent Docker Container..."

# Activate Python virtual environment
source /opt/venv/bin/activate

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping PathRAG server (PID ${PATHRAG_PID})..."
    kill "${PATHRAG_PID}" 2>/dev/null || true
    exit 0
}
trap cleanup SIGTERM SIGINT EXIT

# Set default environment variables if not provided
export PATHRAG_API_URL="${PATHRAG_API_URL:-http://localhost:8123/api/context}"
export NODE_ENV="${NODE_ENV:-production}"

echo "🌐 Using PATHRAG_API_URL=${PATHRAG_API_URL}"
echo "🔧 NODE_ENV=${NODE_ENV}"

# Start PathRAG API server in background
echo "🚀 Starting PathRAG API server..."
cd /app/pathrag
python3 pathrag_api.py &
PATHRAG_PID=$!
echo "📍 PathRAG PID: ${PATHRAG_PID}"

# Wait a moment for PathRAG to start
sleep 3

# Return to app directory
cd /app

# Start Next.js application
echo "🚀 Starting Next.js application..."
exec pnpm start
