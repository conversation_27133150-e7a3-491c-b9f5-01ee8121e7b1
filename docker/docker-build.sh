#!/usr/bin/env bash
set -e

echo "🐳 Building CS Agent Docker Image..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [[ ! -f ".env" ]]; then
    print_warning ".env file not found. Creating from template..."
    if [[ -f ".env.example" ]]; then
        cp .env.example .env
        print_status "Created .env from .env.example"
    else
        print_error ".env file is required. Please create it with necessary environment variables."
        exit 1
    fi
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build the Docker image
print_status "Building Docker image..."
docker build -t cs-agent:latest .

if [[ $? -eq 0 ]]; then
    print_status "✅ Docker image built successfully!"
    
    echo ""
    print_status "To run the container:"
    echo "  docker-compose up -d"
    echo ""
    print_status "Or run directly:"
    echo "  docker run -p 3000:3000 -p 8123:8123 --env-file .env cs-agent:latest"
    echo ""
    print_status "Access the application at:"
    echo "  Web Interface: http://localhost:3000"
    echo "  PathRAG API: http://localhost:8123"
    
else
    print_error "❌ Docker build failed!"
    exit 1
fi
