# Use Node.js 18 as base image
FROM node:18-bullseye-slim

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    bash \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm
RUN npm install -g pnpm@9.12.3

# Set working directory
WORKDIR /app

# Create Python virtual environment
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy Python requirements first for better caching
COPY pathrag/requirements.txt ./pathrag/
RUN pip install --no-cache-dir -r pathrag/requirements.txt

# Copy package files for Node.js dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build Next.js application
ENV NODE_ENV=production
RUN pnpm build

# Create entrypoint script
COPY docker/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expose ports
EXPOSE 3000 8123

# Set default environment variables
ENV PATHRAG_API_URL=http://localhost:8123/api/context
ENV NODE_ENV=production

# Use entrypoint script
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
