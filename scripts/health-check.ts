#!/usr/bin/env tsx

import { config } from 'dotenv';
import postgres from 'postgres';

// Load environment variables
config({
  path: process.env.NODE_ENV === 'production' ? undefined : '.env',
});

async function healthCheck() {
  console.log('🔍 Running pre-build health check...');
  
  // Check required environment variables
  const requiredEnvVars = [
    'POSTGRES_URL',
    'AUTH_SECRET',
    'GOOGLE_GENERATIVE_AI_API_KEY',
    'BLOB_READ_WRITE_TOKEN'
  ];

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingEnvVars);
    process.exit(1);
  }

  console.log('✅ All required environment variables are present');

  // Test database connection
  if (process.env.POSTGRES_URL) {
    try {
      console.log('🔍 Testing database connection...');
      
      const connection = postgres(process.env.POSTGRES_URL, { 
        max: 1,
        connect_timeout: 10,
        ssl: 'require'
      });
      
      await connection`SELECT 1 as test`;
      await connection.end();
      
      console.log('✅ Database connection successful');
    } catch (error) {
      console.warn('⚠️  Database connection failed:', (error as Error).message);
      
      if (process.env.NODE_ENV === 'production') {
        console.log('⚠️  Continuing build despite database connection failure (production mode)');
      } else {
        console.error('❌ Database connection required for development build');
        process.exit(1);
      }
    }
  }

  console.log('✅ Health check completed successfully');
}

healthCheck().catch((error) => {
  console.error('❌ Health check failed:', error);
  process.exit(1);
});
