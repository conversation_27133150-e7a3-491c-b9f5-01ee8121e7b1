# Quy tắc Tài liệu

## 1. README
- <PERSON><PERSON><PERSON> có `README.md` ở root, mô tả mục tiêu, c<PERSON><PERSON> chạy, c<PERSON>u trúc project.

## 2. Tài liệu API
- Đặt trong `docs/`, mô tả endpoint, input/output, ví dụ.

## 3. <PERSON><PERSON><PERSON> li<PERSON>u AI
- Ghi chú rõ model, prompt, g<PERSON><PERSON><PERSON>, cách sử dụng AI.

## 4. Comment code
- Comment cho hàm <PERSON>h<PERSON> tạ<PERSON>, gi<PERSON><PERSON> thích logic AI, xử lý bảo mật.

## 5. V<PERSON> dụ
- `docs/README.md`
- `docs/DOCKER-QUICKSTART.md`
- Comment trong `lib/ai/models.ts`