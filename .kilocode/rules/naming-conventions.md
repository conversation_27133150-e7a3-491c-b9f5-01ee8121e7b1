# Quy tắc Đặt Tên

## 1. <PERSON><PERSON><PERSON> mục & file
- <PERSON><PERSON><PERSON> mục: kebab-case (`app/embed/`, `lib/ai/`).
- File TypeScript: camelCase hoặc PascalCase cho component, snake_case cho migration SQL.

## 2. <PERSON><PERSON><PERSON><PERSON>, hàm, type
- Biến/hàm: camelCase.
- Component: PascalCase.
- Type/interface: Pascal<PERSON>ase, kết thúc bằng `Props` nếu là props.

## 3. API route
- Đường dẫn API: dùng kebab-case, rõ nghĩa (`/api/embed/chat`).

## 4. Ví dụ
- `components/chat-header.tsx`
- `lib/ai/openrouter.ts`
- `app/(chat)/api/chat/route.ts`
- `type UserProps = { ... }`