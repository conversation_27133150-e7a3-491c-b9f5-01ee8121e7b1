# Quy tắc <PERSON> thử

## 1. V<PERSON><PERSON><PERSON> test cho logic quan trọng
- <PERSON><PERSON><PERSON> cả logic AI, x<PERSON><PERSON> thực, upload file, xử lý dữ liệu phải có test.

## 2. Vị trí file test
- Đặt trong thư mục `tests/` hoặc cùng thư mục với file cần test, tên file `*.test.ts(x)`.

## 3. <PERSON><PERSON><PERSON> cụ
- Ưu tiên Playwright cho end-to-end, Jest/Testing Library cho unit test.

## 4. Mock dữ liệu nhạy cảm
- Không dùng API key thật, phải mock hoặc dùng biến môi trường giả.

## 5. Ví dụ

```typescript
// tests/prompts/utils.ts
import { buildPrompt } from "lib/ai/prompts";

test("buildPrompt tạo prompt đúng", () => {
  expect(buildPrompt("hello")).toContain("hello");
});