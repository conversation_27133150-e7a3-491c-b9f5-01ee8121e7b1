# Quy tắc Code Style

## 1. Sử dụng TypeScript
- Tất cả code frontend và backend phải dùng TypeScript.
- Không sử dụng `any` trừ khi thực sự cần thiết, ph<PERSON><PERSON> chú thích lý do.

## 2. Định dạng code
- Sử dụng Prettier hoặc Biome để định dạng code tự động.
- D<PERSON>u nháy kép `"`, indent 2 spaces, không dùng dấu `;` kết thúc dòng.

## 3. Import
- Import theo thứ tự: thư viện ngoài → alias (`lib/`, `hooks/`, `components/`) → local.
- Không import không dùng.

## 4. React/Next.js
- Dùng function component, không dùng class component.
- Props phải có type rõ ràng.
- Không dùng state global ngoài context/provider.
- Không dùng `any` cho props hoặc state.

## 5. Đặt tên biến, hàm
- <PERSON><PERSON>ến/hàm: camelCase.
- Component: PascalCase.

## 6. V<PERSON> dụ

```typescript
import { useState } from "react";
import { fetchData } from "lib/utils";

type Props = {
  userId: string;
};

export function UserCard({ userId }: Props) {
  const [data, setData] = useState(null);
  // ...
}