# Quy tắc <PERSON> trúc Project

## 1. <PERSON><PERSON><PERSON> m<PERSON>
- `app/`: Next.js pages, API routes.
- `components/`: React component tái sử dụng.
- `lib/`: Logic nghiệp vụ, AI, DB, auth.
- `hooks/`: Custom React hooks.
- `public/`: Asset tĩnh.
- `tests/`: Test.
- `docs/`: T<PERSON><PERSON> li<PERSON>.

## 2. <PERSON>ân tách rõ ràng
- Không trộn code frontend/backend.
- API đặt trong `app/api/` hoặc `app/(chat)/api/`.

## 3. <PERSON>ript & c<PERSON>u hình
- Script đặt trong `scripts/`.
- Dockerfile, config đặt trong `docker/`.

## 4. V<PERSON> dụ

```
.
├── app/
├── components/
├── lib/
├── hooks/
├── public/
├── tests/
├── docs/
├── scripts/
├── docker/