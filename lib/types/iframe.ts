export type UserSource = 'internal' | 'iframe' | 'api';

export interface ExternalUser {
  externalId: string;
  source: UserSource;
  metadata?: Record<string, any>;
}

export interface IframeMessage {
  type: 'USER_DATA' | 'CHAT_EVENT' | 'ERROR';
  payload: any;
  origin: string;
}

export interface IframeChatProps {
  userId?: string;
  allowedOrigins?: string[];
  onAuth?: (user: ExternalUser) => void;
  onError?: (error: string) => void;
}
