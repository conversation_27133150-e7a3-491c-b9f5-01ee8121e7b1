import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

// Load environment variables from .env file for local development
// In production (Vercel), environment variables are provided by the platform
config({
  path: process.env.NODE_ENV === 'production' ? undefined : '.env',
});

const runMigrate = async () => {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL is not defined');
  }

  // Skip migration in certain environments or if explicitly disabled
  if (process.env.SKIP_MIGRATION === 'true') {
    console.log('🔄 Migration skipped (SKIP_MIGRATION=true)');
    process.exit(0);
  }

  try {
    const connection = postgres(process.env.POSTGRES_URL, { 
      max: 1,
      ssl: 'require'
    });
    const db = drizzle(connection);

    console.log('⏳ Running migrations...');

    const start = Date.now();
    await migrate(db, { migrationsFolder: './lib/db/migrations' });
    const end = Date.now();

    console.log('✅ Migrations completed in', end - start, 'ms');
    
    await connection.end();
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    
    // In production, we might want to continue build even if migration fails
    // This allows the app to still deploy and potentially fix issues manually
    if (process.env.NODE_ENV === 'production' && process.env.IGNORE_MIGRATION_ERRORS === 'true') {
      console.warn('⚠️  Continuing build despite migration failure (production mode)');
      process.exit(0);
    }
    
    process.exit(1);
  }
};

runMigrate().catch((err) => {
  console.error('❌ Migration failed');
  console.error(err);
  process.exit(1);
});
