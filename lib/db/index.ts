import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

let connection: postgres.Sql;

if (!process.env.POSTGRES_URL) {
  throw new Error('POSTGRES_URL is not defined');
}

// Check if we're in development or production
const isDevelopment = process.env.NODE_ENV !== 'production';

// Create connection with appropriate settings
if (isDevelopment) {
  // For development, use simple connection with SSL
  connection = postgres(process.env.POSTGRES_URL, {
    max: 20,
    idle_timeout: 20,
    connect_timeout: 10,
    ssl: 'require',
  });
} else {
  // For production (Vercel), use optimized settings
  connection = postgres(process.env.POSTGRES_URL, {
    max: 1,
    ssl: 'require',
  });
}

export const db = drizzle(connection);
// Graceful shutdown
process.on('SIGINT', async () => {
  await connection.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await connection.end();
  process.exit(0);
});
