// pathrag-knowledge.ts - Improved PathRAG integration with better error handling
import { tool } from 'ai';
import { z } from 'zod';

function formatKnowledgeResponse(contextData: any): string {
  let content: string;

  if (typeof contextData === 'string') {
    content = contextData;
  } else if (contextData?.context_documents?.length) {
    content = contextData.context_documents
      .map((doc: any) => {
        const docContent = doc.content || '';
        const filename = doc.filename || doc.file || 'document';
        const score = doc.relevance_score ? ` (relevance: ${doc.relevance_score})` : '';
        return `**${filename}${score}**\n\n${docContent}`;
      })
      .join('\n\n---\n\n');
  } else {
    content = contextData ? JSON.stringify(contextData, null, 2) : 'Không có nội dung';
  }

  return String(content)
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\r/g, '\r')
    .replace(/\n\n### Tài liệu tham khảo:[\s\S]*$/g, '')
    .replace(/^### (.+)$/gm, '**$1**')
    .replace(/^## (.+)$/gm, '**$1**')
    .replace(/^# (.+)$/gm, '**$1**');
}

function extractSourceUrls(content: string): string[] {
  const urlRegex = /https?:\/\/[^\s\)\]\},<"']+/g;
  const urls = content.match(urlRegex) || [];
  
  return [...new Set(urls
    .map(url => url.replace(/[.,:;!?]+$/, ''))
    .filter(url => {
      try { new URL(url); return true; } catch { return false; }
    }))];
}

const PATHRAG_API_URL = process.env.PATHRAG_API_URL || 'http://localhost:8123/api/context';

interface PathRAGResponse {
  success: boolean;
  message: string;
  data?: {
    query: string;
    keywords?: string[];
    context_documents: { 
      content: string; 
      filename?: string; 
      file?: string;
      relevance_score?: number; 
      metadata?: any;
    }[];
    total_documents: number;
    message?: string;
    processing_type?: string;
  };
  error?: string;
}

export const getPathRAGKnowledge = tool({
  description: `
  Advanced knowledge retrieval from PathRAG knowledge base.

  SỬ DỤNG KHI:
  - Câu hỏi về sản phẩm/dịch vụ: "sản phẩm X có tính năng gì?"
  - Chính sách công ty: "chính sách bảo hành", "quy trình đổi trả"
  - Hướng dẫn sử dụng: "cách cài đặt", "cách sử dụng tính năng Y"
  - Thông tin kỹ thuật: "yêu cầu hệ thống", "API documentation"
  - Troubleshooting: "lỗi X xử lý như thế nào"

  KHÔNG SỬ DỤNG KHI:
  - Câu hỏi chào hỏi: "xin chào", "cảm ơn"
  - Thông tin thời tiết (dùng getWeather)
  - Thông tin thời gian hiện tại
  - Câu hỏi về toán học/khoa học chung
  - Yêu cầu tạo content mới
  `,
  parameters: z.object({
    query: z.string().describe('Câu hỏi hoặc từ khóa cần tìm kiếm trong knowledge base'),
    context: z.string().optional().describe('Ngữ cảnh bổ sung để tìm kiếm chính xác hơn (manual context từ user)'),
    max_results: z.number().optional().default(5).describe('Số lượng documents tối đa trả về (default: 5)')
  }),
  execute: async ({ query, context, max_results = 5 }) => {
    try {
      if (!query.trim()) {
        console.log('❌ PathRAG: Empty query provided');
        return {
          success: false,
          message: 'Query không được để trống',
          data: null,
          error: 'Empty query'
        };
      }

      console.log(`🔍 PathRAG API Request:`, {
        query: query.slice(0, 100) + (query.length > 100 ? '...' : ''),
        context: context ? context.slice(0, 50) + (context.length > 50 ? '...' : '') : undefined,
        max_results,
        url: PATHRAG_API_URL
      });

      const finalQuery = context ? `${query} ${context}` : query;

      const response = await fetch(PATHRAG_API_URL, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: finalQuery,
          max_results
        }),
      });

      console.log(`📡 PathRAG API Response Status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.error(`❌ PathRAG API HTTP Error: ${response.status} ${response.statusText}`);
        
        let errorDetail = '';
        try {
          const errorData = await response.text();
          errorDetail = errorData ? ` - ${errorData.slice(0, 200)}` : '';
        } catch (e) {
          console.log('Could not parse error response');
        }

        return {
          success: false,
          message: `Lỗi kết nối PathRAG knowledge base (${response.status}). Vui lòng kiểm tra server PathRAG có đang chạy không.`,
          data: null,
          error: `HTTP ${response.status}${errorDetail}`
        };
      }

      const data: PathRAGResponse = await response.json();
      console.log('📋 PathRAG API Response:', {
        success: data.success,
        message: data.message,
        totalDocs: data.data?.total_documents,
        contextDocsCount: data.data?.context_documents?.length,
        processingType: data.data?.processing_type,
        hasKeywords: !!data.data?.keywords?.length
      });

      if (!data.success) {
        console.log('❌ PathRAG API returned unsuccessful response:', data.message || data.error);
        return {
          success: false,
          message: data.message || 'PathRAG API trả về lỗi không xác định',
          data: null,
          error: data.error || 'API returned success: false'
        };
      }

      if (!data.data || !data.data.context_documents) {
        console.log('❌ PathRAG API returned no data or context_documents:', data);
        return {
          success: false,
          message: 'Không tìm thấy thông tin phù hợp trong knowledge base',
          data: null,
          error: 'No context_documents in response'
        };
      }

      if (data.data.context_documents.length === 0) {
        console.log('⚠️ PathRAG API returned empty context_documents array');
        return {
          success: false,
          message: 'Không tìm thấy tài liệu nào phù hợp với câu hỏi của bạn',
          data: null,
          error: 'Empty context_documents array'
        };
      }

      // Format response và extract URLs
      const formattedContent = formatKnowledgeResponse(data.data);
      const sourceUrls = extractSourceUrls(formattedContent);

      console.log('✅ PathRAG Knowledge retrieved successfully:', {
        totalDocs: data.data.total_documents,
        formattedLength: formattedContent.length,
        sourceUrls: sourceUrls.length,
        keywords: data.data.keywords?.length || 0
      });

      return {
        success: true,
        message: `Tìm thấy ${data.data.total_documents} tài liệu từ PathRAG knowledge base`,
        data: formattedContent,
        sourceUrls: sourceUrls.length > 0 ? sourceUrls : undefined,
        query: finalQuery,
        timestamp: new Date().toISOString(),
        documentCount: data.data.total_documents,
        keywords: data.data.keywords || [],
        processingType: data.data.processing_type || 'context_only'
      };

    } catch (err) {
      console.error('💥 PathRAG knowledge retrieval error:', {
        error: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        query: query.slice(0, 100),
        url: PATHRAG_API_URL
      });

      return {
        success: false,
        message: 'Không thể kết nối đến PathRAG knowledge base. Vui lòng kiểm tra server có đang chạy và thử lại.',
        data: null,
        error: err instanceof Error ? err.message : 'Unknown network error'
      };
    }
  },
});
