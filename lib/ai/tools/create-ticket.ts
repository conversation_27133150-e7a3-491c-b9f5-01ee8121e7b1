import { tool } from 'ai';
import { z } from 'zod';

export const createTicket = tool({
  description: `
  Tạo ticket hỗ trợ chuyên nghiệp để kết nối người dùng với support team.
  
  ⚠️ QUAN TRỌNG: Tool này chỉ được sử dụng sau khi đã research getPathRAGKnowledge và không tìm thấy giải pháp.

  <usage_guidelines>
  SỬ DỤNG KHI:
  - Đã tìm hiểu knowledge base nhưng KHÔNG có thông tin giải quyết
  - User hỏi cùng vấn đề 2-3 lần mà chưa được giải quyết thỏa đáng
  - Vấn đề kỹ thuật phức tạp vượt quá knowledge base
  - User xác nhận muốn escalate sau khi đã được hỗ trợ một phần

  KHÔNG SỬ DỤNG KHI:
  - User chỉ nói "tạo ticket" mà chưa mô tả vấn đề cụ thể
  - Câu hỏi có thể trả lời bằng knowledge base (phải research trước)
  - Chưa cố gắng giải quyết qua getPathRAGKnowledge

  LUỒNG XỬ LÝ:
  1. User nói "tạo ticket" → Hỏi vấn đề cụ thể
  2. User mô tả vấn đề → Dùng getPathRAGKnowledge research
  3. Nếu knowledge base không có solution → Mới dùng createTicket
  4. Nếu có partial solution nhưng user vẫn cần thêm → Suggest createTicket
  </usage_guidelines>

  <ticket_categories>
  PHÂN LOẠI TICKET:
  - bug: Lỗi phần mềm, tính năng không hoạt động, crash, error messages
  - feature: Yêu cầu tính năng mới, cải tiến, enhancement
  - support: Hỗ trợ sử dụng, hướng dẫn, setup, configuration
  - feedback: Góp ý, đánh giá, phản hồi về sản phẩm/dịch vụ
  - complaint: Khiếu nại, không hài lòng, yêu cầu refund
  - billing: Vấn đề thanh toán, hóa đơn, pricing
  - account: Vấn đề tài khoản, quyền truy cập, authentication
  </ticket_categories>

  <priority_levels>
  ĐỘ ƯU TIÊN:
  - critical: Hệ thống down, mất dữ liệu, bảo mật, không thể làm việc
  - high: Tính năng chính không hoạt động, ảnh hưởng nghiêm trọng business
  - medium: Lỗi ảnh hưởng một phần, yêu cầu cải tiến quan trọng
  - low: Lỗi nhỏ không ảnh hưởng, góp ý, câu hỏi chung
  </priority_levels>

  <auto_categorization>
  TỰ ĐỘNG PHÂN LOẠI:
  - "crash", "lỗi", "không hoạt động", "error", "broken" → bug + high
  - "muốn thêm", "đề xuất", "request", "enhancement" → feature + medium  
  - "không biết cách", "hướng dẫn", "setup", "cấu hình" → support + medium
  - "góp ý", "feedback", "đánh giá", "suggest" → feedback + low
  - "không hài lòng", "khiếu nại", "refund", "complaint" → complaint + high
  - "thanh toán", "billing", "invoice", "charge" → billing + medium
  - "đăng nhập", "tài khoản", "password", "access" → account + high
  </auto_categorization>

  <context_extraction>
  Trích xuất context từ conversation để pre-fill form:
  - problemSummary: Tóm tắt ngắn gọn vấn đề chính (cho title)
  - problemDetails: Mô tả chi tiết từ user + context conversation
  - stepsAttempted: Các giải pháp đã thử trong conversation
  - conversationContext: Toàn bộ ngữ cảnh để support team hiểu background
  </context_extraction>
  `,
  parameters: z.object({
    category: z.string().optional().describe('Loại ticket: bug, feature, support, feedback, complaint, billing, account'),
    priority: z.string().optional().describe('Độ ưu tiên: low, medium, high, critical'),
    problemSummary: z.string().describe('Tóm tắt ngắn gọn vấn đề chính (sẽ làm title)'),
    problemDetails: z.string().describe('Mô tả chi tiết vấn đề từ user và context conversation'),
    stepsAttempted: z.string().optional().describe('Các bước đã thử trong conversation'),
    reasonForEscalation: z.string().describe('Lý do cần escalate: knowledge base không có info, repeated question, complex issue, user request, etc.')
  }),
  execute: async ({ 
    category = 'support', 
    priority = 'medium', 
    problemSummary,
    problemDetails,
    stepsAttempted,
    reasonForEscalation
  }, { _context }: { _context?: any }) => {
    try {
      // Auto-categorize based on problem summary
      const lowerSummary = problemSummary.toLowerCase();
      let autoCategory = category;
      let autoPriority = priority;

      // Smart categorization
      if (lowerSummary.includes('crash') || lowerSummary.includes('lỗi') || 
          lowerSummary.includes('error') || lowerSummary.includes('không hoạt động')) {
        autoCategory = 'bug';
        autoPriority = 'high';
      } else if (lowerSummary.includes('muốn thêm') || lowerSummary.includes('đề xuất') ||
                 lowerSummary.includes('enhancement') || lowerSummary.includes('feature')) {
        autoCategory = 'feature';
        autoPriority = 'medium';
      } else if (lowerSummary.includes('không biết') || lowerSummary.includes('hướng dẫn') ||
                 lowerSummary.includes('setup') || lowerSummary.includes('cấu hình')) {
        autoCategory = 'support';
        autoPriority = 'medium';
      } else if (lowerSummary.includes('khiếu nại') || lowerSummary.includes('không hài lòng') ||
                 lowerSummary.includes('refund') || lowerSummary.includes('complaint')) {
        autoCategory = 'complaint';
        autoPriority = 'high';
      } else if (lowerSummary.includes('đăng nhập') || lowerSummary.includes('tài khoản') ||
                 lowerSummary.includes('password') || lowerSummary.includes('access')) {
        autoCategory = 'account';
        autoPriority = 'high';
      }

      // Format comprehensive description
      let fullDescription = problemDetails;
      
      if (stepsAttempted) {
        fullDescription += `\n\n**Các bước đã thử:**\n${stepsAttempted}`;
      }
      
      fullDescription += `\n\n**Lý do escalate:** ${reasonForEscalation}`;
      fullDescription += `\n\n**Thời gian:** ${new Date().toLocaleString('vi-VN')}`;

      // Try to create actual ticket if tenant and token are available
      const { tenant, token } = _context || {};
      
      if (tenant && token) {
        try {
          const apiUrl = `https://${tenant}/rest/v11_3/Bugs?erased_fields=true&viewed=1`;
          
          console.log('🎫 Creating ticket at:', apiUrl.replace(token, '***'));
          
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              name: problemSummary,
              description: fullDescription,
              user_agreement: true,
              type: 'technical',
              status: 'open',
              priority: autoPriority,
              product: 'ems', // Default product
              severity: autoPriority === 'critical' ? '1' : autoPriority === 'high' ? '2' : '3'
            })
          });

          if (response.ok) {
            const result = await response.json();
            console.log('✅ Ticket created successfully:', result.id || result);
            
            return {
              success: true,
              ticketCreated: true,
              ticketId: result.id || result._id || 'created',
              category: autoCategory,
              priority: autoPriority,
              title: problemSummary,
              description: fullDescription,
              apiResponse: result,
              timestamp: new Date().toISOString(),
              message: `Ticket đã được tạo thành công! Mã ticket: ${result.id || result._id || 'N/A'}`
            };
          } else {
            console.error('❌ Failed to create ticket:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('Error response:', errorText);
            
            // Fallback to form display if API fails
            return {
              success: true,
              ticketCreated: false,
              formType: 'support-ticket',
              category: autoCategory,
              priority: autoPriority,
              title: problemSummary,
              description: fullDescription,
              reasonForEscalation,
              timestamp: new Date().toISOString(),
              message: 'Không thể tạo ticket tự động. Vui lòng liên hệ support team trực tiếp.',
              error: `API Error: ${response.status} - ${errorText}`
            };
          }
        } catch (apiError) {
          console.error('💥 API call failed:', apiError);
          
          // Fallback to form display if API call fails
          return {
            success: true,
            ticketCreated: false,
            formType: 'support-ticket',
            category: autoCategory,
            priority: autoPriority,
            title: problemSummary,
            description: fullDescription,
            reasonForEscalation,
            timestamp: new Date().toISOString(),
            message: 'Không thể kết nối với hệ thống ticket. Vui lòng thử lại sau hoặc liên hệ support team.',
            error: apiError instanceof Error ? apiError.message : 'Network error'
          };
        }
      }

      // Default response when no tenant/token available (display form)
      return {
        success: true,
        ticketCreated: false,
        formType: 'support-ticket',
        category: autoCategory,
        priority: autoPriority,
        title: problemSummary,
        description: fullDescription,
        reasonForEscalation,
        timestamp: new Date().toISOString(),
        message: 'Thông tin ticket đã được chuẩn bị. Vui lòng liên hệ support team để được hỗ trợ.',
        metadata: {
          autoGenerated: true,
          conversationBased: true,
          escalationReason: reasonForEscalation
        }
      };
    } catch (error) {
      console.error('Create ticket tool error:', error);
      return {
        success: false,
        message: 'Không thể tạo ticket. Vui lòng thử lại sau.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
});
