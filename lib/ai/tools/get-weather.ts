// Enhanced get-weather.ts with location name support
import { tool } from 'ai';
import { z } from 'zod';

/**
 * <PERSON>y<PERSON>n đổi tên địa điểm thành tọa độ sử dụng Nominatim API (OpenStreetMap)
 */
async function geocodeLocation(locationName: string): Promise<{ latitude: number; longitude: number; displayName: string } | null> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(locationName)}&format=json&limit=1&accept-language=vi`,
      {
        signal: controller.signal,
        headers: {
          'User-Agent': 'AI-Chatbot/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data || data.length === 0) {
      return null;
    }

    const result = data[0];
    return {
      latitude: Number.parseFloat(result.lat),
      longitude: Number.parseFloat(result.lon),
      displayName: result.display_name
    };

  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

export const getWeather = tool({
  description: `
  Lấy thông tin thời tiết hiện tại và dự báo cho một vị trí cụ thể.

  <usage_guidelines>
  SỬ DỤNG KHI:
  - Người dùng hỏi về thời tiết: "thời tiết hôm nay", "trời có mưa không"
  - Cần thông tin thời tiết cho planning: "mai đi picnic có được không"
  - So sánh thời tiết nhiều địa điểm

  KHÔNG SỬ DỤNG KHI:
  - Câu hỏi về khí hậu lịch sử (dùng knowledge base)
  - Thông tin về thiên tai (dùng knowledge base)
  - Câu hỏi chung về thời tiết (không có địa điểm cụ thể)
  </usage_guidelines>

  <input_formats>
  CÁCH NHẬP ĐỊA ĐIỂM (ưu tiên theo thứ tự):
  1. Tên thành phố tiếng Việt: "Hà Nội", "TP.HCM", "Đà Nẵng"
  2. Tên quốc tế: "Paris", "Tokyo", "New York"
  3. Tọa độ chính xác: latitude + longitude (khi có GPS)

  VÍ DỤ HỢP LỆ:
  - location: "Hà Nội" ✅
  - location: "Ho Chi Minh City" ✅
  - latitude: 21.0285, longitude: 105.8542 ✅

  VÍ DỤ KHÔNG HỢP LỆ:
  - location: "ở đây" ❌ (không cụ thể)
  - location: "miền Bắc" ❌ (quá rộng)
  </input_formats>

  <output_data>
  DỮ LIỆU TRẢ VỀ:
  - Nhiệt độ hiện tại và cảm giác thực tế
  - Độ ẩm, tốc độ gió, hướng gió
  - Mô tả thời tiết (nắng, mưa, mây...)
  - Dự báo 24h và 3 ngày tới
  - Thời gian mặt trời mọc/lặn
  - Chỉ số UV và tầm nhìn
  </output_data>

  <error_handling>
  XỬ LÝ LỖI:
  - Địa điểm không tìm thấy → Gợi ý địa điểm gần nhất
  - API timeout → Thử lại với cache data
  - Tọa độ không hợp lệ → Yêu cầu nhập lại
  </error_handling>
  `,
  parameters: z.object({
    location: z.string().optional().describe('Tên địa điểm (ưu tiên): "Hà Nội", "TP.HCM", "New York"'),
    latitude: z.number().min(-90).max(90).optional().describe('Vĩ độ (chỉ khi không có location)'),
    longitude: z.number().min(-180).max(180).optional().describe('Kinh độ (chỉ khi không có location)')
  }),
  execute: async ({ location, latitude, longitude }) => {
    try {
      let finalLat: number;
      let finalLon: number;
      let finalLocationName: string;

      // Ưu tiên sử dụng location name nếu có
      if (location) {
        const geocodeResult = await geocodeLocation(location);

        if (!geocodeResult) {
          return {
            success: false,
            message: `Không tìm thấy địa điểm "${location}". Vui lòng thử với tên địa điểm khác hoặc cung cấp tọa độ.`,
            suggestions: ['Hà Nội', 'TP. Hồ Chí Minh', 'Đà Nẵng', 'Cần Thơ']
          };
        }

        finalLat = geocodeResult.latitude;
        finalLon = geocodeResult.longitude;
        finalLocationName = geocodeResult.displayName;
      }
      // Fallback sử dụng tọa độ
      else if (latitude !== undefined && longitude !== undefined) {
        finalLat = latitude;
        finalLon = longitude;
        finalLocationName = `${latitude}, ${longitude}`;
      }
      // Không có thông tin vị trí
      else {
        return {
          success: false,
          message: 'Vui lòng cung cấp tên địa điểm (ví dụ: "Hà Nội") hoặc tọa độ (latitude, longitude).',
          examples: ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Paris', 'Tokyo']
        };
      }

      // Lấy dữ liệu thời tiết
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(
        `https://api.open-meteo.com/v1/forecast?latitude=${finalLat}&longitude=${finalLon}&current=temperature_2m,relative_humidity_2m,weather_code,wind_speed_10m,apparent_temperature&hourly=temperature_2m,weather_code&daily=sunrise,sunset,temperature_2m_max,temperature_2m_min,weather_code&timezone=auto&forecast_days=3`,
        { signal: controller.signal }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`);
      }

      const weatherData = await response.json();

      // Validate response
      if (!weatherData.current) {
        throw new Error('Invalid weather data received');
      }

      return {
        success: true,
        location: finalLocationName,
        coordinates: { latitude: finalLat, longitude: finalLon },
        data: weatherData,
        timestamp: new Date().toISOString()
      };

    } catch (err) {
      console.error('Weather API error:', err);

      if (err instanceof Error && err.name === 'AbortError') {
        return {
          success: false,
          message: 'Yêu cầu thời tiết bị timeout. Vui lòng thử lại.',
        };
      }

      return {
        success: false,
        message: 'Không thể lấy thông tin thời tiết lúc này. Vui lòng thử lại sau.',
        error: err instanceof Error ? err.message : 'Unknown error'
      };
    }
  },
});
