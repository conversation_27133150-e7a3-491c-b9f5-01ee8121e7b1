// Enhanced entitlements.ts with rate limiting
import type { UserType } from '@/app/(auth)/auth';
import type { ChatModel } from './models';

interface Entitlements {
  maxMessagesPerDay: number;
  maxMessagesPerHour: number;
  availableChatModelIds: Array<ChatModel['id']>;
  canUseAdvancedTools: boolean;
  maxToolCallsPerMessage: number;
}

export const entitlementsByUserType: Record<UserType, Entitlements> = {
  guest: {
    maxMessagesPerDay: 50,
    maxMessagesPerHour: 10,
    availableChatModelIds: ['chat-model'],
    canUseAdvancedTools: false,
    maxToolCallsPerMessage: 3,
  },
  regular: {
    maxMessagesPerDay: 500,
    maxMessagesPerHour: 50,
    availableChatModelIds: ['chat-model', 'chat-model-reasoning'],
    canUseAdvancedTools: true,
    maxToolCallsPerMessage: 10,
  },
  external: {
    maxMessagesPerDay: 200,
    maxMessagesPerHour: 30,
    availableChatModelIds: ['chat-model'],
    canUseAdvancedTools: true,
    maxToolCallsPerMessage: 5,
  },
};

// Validation function for usage limits
export function validateUsage(userType: UserType, currentUsage: {
  messagesInDay: number;
  messagesInHour: number;
  toolCallsInMessage?: number;
}): { allowed: boolean; reason?: string } {
  const entitlements = entitlementsByUserType[userType];

  if (currentUsage.messagesInDay >= entitlements.maxMessagesPerDay) {
    return { allowed: false, reason: 'Đã đạt giới hạn tin nhắn trong ngày' };
  }

  if (currentUsage.messagesInHour >= entitlements.maxMessagesPerHour) {
    return { allowed: false, reason: 'Đã đạt giới hạn tin nhắn trong giờ' };
  }

  if (currentUsage.toolCallsInMessage &&
      currentUsage.toolCallsInMessage > entitlements.maxToolCallsPerMessage) {
    return { allowed: false, reason: 'Quá nhiều tool calls trong một tin nhắn' };
  }

  return { allowed: true };
}
