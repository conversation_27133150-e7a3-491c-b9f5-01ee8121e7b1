// OpenRouter provider for AI SDK
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

const openrouterFactory = (() => {
  let instance: ReturnType<typeof createOpenRouter> | null = null;
  return () => {
    if (!instance) {
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) throw new Error('OPENROUTER_API_KEY is not set');
      instance = createOpenRouter({ apiKey });
    }
    return instance;
  };
})();

export function openrouter(modelName: string) {
  return openrouterFactory()(modelName);
}

