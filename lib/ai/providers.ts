import {
  customProvider,
  wrapLanguageModel,
  type LanguageModelV1Middleware,
  type LanguageModelV1StreamPart,
} from 'ai';
import { google } from '@ai-sdk/google';

// Type for generate function result
interface GenerateResult {
  text: string;
  toolCalls?: LanguageModelV1StreamPart[];
  toolResults?: LanguageModelV1StreamPart[];
  finishReason: string;
  usage: { promptTokens: number; completionTokens: number; totalTokens?: number };
  rawResponse?: { headers?: Record<string, string> };
  warnings?: string[];
  logprobs?: Array<LanguageModelV1StreamPart>;
}

// Middleware for default settings
const defaultSettingsMiddleware: LanguageModelV1Middleware = {
  wrapGenerate: async (options) => {
    const start = Date.now();
    try {
      const result = await options.doGenerate();
      console.log(`🤖 Generation finished in ${Date.now() - start}ms`);
      return result;
    } catch (err) {
      console.error('❌ Generation error:', err);
      throw err;
    }
  },
  wrapStream: async (opts) => opts.doStream(),
};

// Middleware for performance logging
const performanceMiddleware: LanguageModelV1Middleware = {
  wrapGenerate: async (options) => {
    const start = Date.now();
    console.log('⏳ Starting generation');
    try {
      const res = await options.doGenerate();
      const dur = Date.now() - start;
      const tokens = res.usage.promptTokens + res.usage.completionTokens;
      console.log(`✅ Generated in ${dur}ms, ${tokens} tokens`);
      return res;
    } catch (err) {
      console.error(`❌ Failed after ${Date.now() - start}ms`, err);
      throw err;
    }
  },
  wrapStream: async (options) => {
    console.log('⏳ Starting stream');
    try {
      const stream = await options.doStream();
      console.log('🌊 Stream active');
      return stream;
    } catch (err) {
      console.error('❌ Stream error', err);
      throw err;
    }
  },
};

// Factory for Google model instances
function createGoogleModel(modelId: string, opts: { cachedContent?: string } = {}) {
  return google(modelId, { structuredOutputs: true, ...opts });
}

// Export provider
export const myProvider = customProvider({
  languageModels: {
    'chat-model': wrapLanguageModel({ model: createGoogleModel('gemini-2.5-flash-preview-04-17'), middleware: [defaultSettingsMiddleware, performanceMiddleware] }),
    'chat-model-reasoning': wrapLanguageModel({ model: createGoogleModel('gemini-2.5-flash-preview-04-17'), middleware: [defaultSettingsMiddleware, performanceMiddleware] }),
    'title-model': wrapLanguageModel({ model: google('gemini-2.0-flash-lite'), middleware: [defaultSettingsMiddleware, performanceMiddleware] }),
  },
});
