'use client';

import { useChat } from '@ai-sdk/react';
import { useEffect, useState } from 'react';

export function DebugPanel({ chatId }: { chatId: string }) {
  const { messages, status, data } = useChat({ id: chatId });
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    const log = `[${new Date().toLocaleTimeString()}] Messages: ${messages.length}, Status: ${status}, Data: ${data?.length || 0}`;
    setLogs(prev => [...prev.slice(-10), log]); // Keep last 10 logs
  }, [messages, status, data]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg max-w-md text-xs font-mono z-50">
      <div className="font-bold mb-2">🔍 Debug Info</div>
      <div>Chat ID: {chatId}</div>
      <div>Messages: {messages.length}</div>
      <div>Status: {status}</div>
      <div>Data Length: {data?.length || 0}</div>
      <div className="mt-2 max-h-32 overflow-y-auto">
        {logs.map((log, i) => (
          <div key={i} className="text-xs opacity-80">{log}</div>
        ))}
      </div>
      <div className="mt-2 text-xs opacity-60">
        Last message: {messages[messages.length - 1]?.content?.slice(0, 30) || 'None'}...
      </div>
    </div>
  );
}
