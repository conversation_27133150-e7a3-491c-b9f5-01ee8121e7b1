'use client';

import { Chat } from './chat';
import type { UIMessage } from 'ai';
import type { Session } from 'next-auth';

export function EmbedChatWrapper({
  id,
  session,
  initialMessages = [],
  initialChatModel = 'gemini-2.0-flash-lite',
  initialVisibilityType = 'private',
  isReadonly = false,
  autoResume = false,
}: {
  id: string;
  session: Session;
  initialMessages?: Array<UIMessage>;
  initialChatModel?: string;
  initialVisibilityType?: 'private' | 'public';
  isReadonly?: boolean;
  autoResume?: boolean;
}) {
  return (
    <Chat
      id={id}
      initialMessages={initialMessages}
      initialChatModel={initialChatModel}
      initialVisibilityType={initialVisibilityType}
      isReadonly={isReadonly}
      session={session}
      autoResume={autoResume}
    />
  );
}