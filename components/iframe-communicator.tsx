'use client';

import { useEffect, useCallback } from 'react';
import type { IframeMessage } from '@/lib/types/iframe';

export interface IframeCommunicatorProps {
  allowedOrigins?: string[];
  onMessage?: (message: IframeMessage) => void;
  onError?: (error: string) => void;
}

export function useIframeCommunication({
  allowedOrigins = [],
  onMessage,
  onError,
}: IframeCommunicatorProps) {
  const sendMessage = useCallback((message: Omit<IframeMessage, 'origin'>, targetOrigin = '*') => {
    if (window.parent && window.parent !== window) {
      const fullMessage: IframeMessage = {
        ...message,
        origin: window.location.origin,
      };
      window.parent.postMessage(fullMessage, targetOrigin);
    }
  }, []);

  const handleMessage = useCallback((event: MessageEvent) => {
    // Validate origin if restrictions are set
    if (allowedOrigins.length > 0) {
      const isAllowed = allowedOrigins.some(allowed => {
        if (allowed === '*') return true;
        if (allowed.startsWith('*.')) {
          const domain = allowed.slice(2);
          return event.origin.endsWith(domain);
        }
        return event.origin === allowed;
      });

      if (!isAllowed) {
        onError?.(`Unauthorized origin: ${event.origin}`);
        return;
      }
    }

    const message = event.data as IframeMessage;

    // Handle specific message types
    switch (message.type) {
      case 'ERROR':
        onError?.(message.payload?.message || 'Unknown error');
        break;

      default:
        onMessage?.(message);
    }
  }, [allowedOrigins, onMessage, onError]);

  useEffect(() => {
    window.addEventListener('message', handleMessage);

    // Send ready message to parent
    sendMessage({
      type: 'CHAT_EVENT',
      payload: { event: 'iframe_ready' },
    });

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage, sendMessage]);

  return {
    sendMessage,
    isIframe: typeof window !== 'undefined' && window.self !== window.top,
    parentOrigin: typeof window !== 'undefined' && document.referrer
      ? new URL(document.referrer).origin
      : null,
  };
}

// React component wrapper
export function IframeCommunicator(props: IframeCommunicatorProps) {
  useIframeCommunication(props);
  return null; // This is a hook-only component
}
