'use client';

export function BasicEmbed({
  id,
  session,
}: {
  id: string;
  session: any;
}) {
  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Simple Header */}
      <header className="flex sticky top-0 bg-background py-1.5 items-center px-4 border-b">
        <h1 className="text-lg font-semibold">Chat SDK - Embed Mode</h1>
        <div className="ml-auto text-sm text-muted-foreground">
          {session?.user?.email || 'Guest'}
        </div>
      </header>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center text-muted-foreground">
            <p>Welcome to Chat SDK Embed Mode</p>
            <p className="text-xs mt-2">Chat ID: {id}</p>
            <p className="text-xs">User: {session?.user?.email || 'Guest'}</p>
            <p className="text-xs">Source: {session?.user?.source || 'N/A'}</p>
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <p className="text-sm">This is a basic embed component for testing.</p>
              <p className="text-xs mt-2">No complex hooks or dependencies.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Simple Input */}
      <div className="border-t p-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Type your message..."
              className="flex-1 px-3 py-2 border border-input bg-background rounded-md text-sm"
              disabled
            />
            <button
              type="button"
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90"
              disabled
            >
              Send
            </button>
          </div>
          <p className="text-xs text-muted-foreground mt-2 text-center">
            Input disabled for testing purposes
          </p>
        </div>
      </div>
    </div>
  );
}