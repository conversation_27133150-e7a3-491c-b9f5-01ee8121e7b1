'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface SupportTicketProps {
  category?: string;
  priority?: string;
  reason?: string;
  title?: string;
  description?: string;
  reasonForEscalation?: string;
  timestamp?: string;
  metadata?: {
    autoGenerated?: boolean;
    conversationBased?: boolean;
    escalationReason?: string;
  };
}

export function SupportTicket({ 
  category = 'support', 
  priority = 'medium',
  reason,
  title: initialTitle = '',
  description: initialDescription = '',
  reasonForEscalation,
  timestamp,
  metadata
}: SupportTicketProps) {
  // Debug logging
  console.log('SupportTicket Props:', {
    category,
    priority,
    reason,
    initialTitle,
    initialDescription,
    reasonForEscalation,
    timestamp,
    metadata
  });

  const [title, setTitle] = useState(initialTitle);
  const [description, setDescription] = useState(
    initialDescription || reason || ''
  );
  const [selectedCategory, setSelectedCategory] = useState(category);
  const [selectedPriority, setSelectedPriority] = useState(priority);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  // Show confirmation if we have enhanced data OR if auto-generated
  const [showConfirmation, setShowConfirmation] = useState(
    Boolean(metadata?.autoGenerated || initialTitle || reasonForEscalation)
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !description.trim()) return;

    setIsSubmitting(true);
    
    // TODO: Implement actual submission logic
    const ticketData = {
      title: title.trim(),
      description: description.trim(),
      category: selectedCategory,
      priority: selectedPriority,
      timestamp: new Date().toISOString()
    };
    
    console.log('Ticket data:', ticketData);
    
    // Simulate submission delay
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 1500);
  };

  const getCategoryLabel = (cat: string) => {
    const labels = {
      bug: 'Báo lỗi',
      feature: 'Yêu cầu tính năng',
      support: 'Hỗ trợ kỹ thuật',
      feedback: 'Phản hồi',
      complaint: 'Khiếu nại', 
      billing: 'Thanh toán',
      account: 'Tài khoản'
    };
    return labels[cat as keyof typeof labels] || 'Hỗ trợ';
  };
  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'text-green-600',
      medium: 'text-yellow-600', 
      high: 'text-red-600'
    };
    return colors[priority as keyof typeof colors] || 'text-gray-600';
  };

  // Show confirmation step if we have pre-filled data
  if (showConfirmation && (metadata?.autoGenerated || initialTitle || reasonForEscalation)) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">
            Xác nhận thông tin ticket
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category" className="text-sm font-medium">Loại ticket</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bug">Báo lỗi</SelectItem>
                    <SelectItem value="feature">Yêu cầu tính năng</SelectItem>
                    <SelectItem value="support">Hỗ trợ kỹ thuật</SelectItem>
                    <SelectItem value="feedback">Phản hồi</SelectItem>
                    <SelectItem value="complaint">Khiếu nại</SelectItem>
                    <SelectItem value="billing">Thanh toán</SelectItem>
                    <SelectItem value="account">Tài khoản</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority" className="text-sm font-medium">Độ ưu tiên</Label>
                <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Thấp</SelectItem>
                    <SelectItem value="medium">Trung bình</SelectItem>
                    <SelectItem value="high">Cao</SelectItem>
                    <SelectItem value="critical">Rất cao</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="title" className="text-sm font-medium">Tiêu đề *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Mô tả ngắn gọn vấn đề của bạn"
                required
                disabled={isSubmitting}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="description" className="text-sm font-medium">Mô tả chi tiết *</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Mô tả chi tiết vấn đề, các bước tái hiện, hoặc yêu cầu của bạn"
                rows={6}
                required
                disabled={isSubmitting}
                className="mt-1 whitespace-pre-wrap"
                style={{ whiteSpace: 'pre-wrap' }}
              />
            </div>

            <div className="flex gap-3 pt-2">
              <Button 
                type="submit"
                className="w-full"
                disabled={isSubmitting || !title.trim() || !description.trim()}
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full size-4 border-b-2 border-white" />
                    Đang gửi...
                  </div>
                ) : (
                  'Gửi ticket'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  }

  if (isSubmitted) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="text-4xl">✓</div>
            <div>
              <h3 className="text-lg font-semibold text-green-600">Ticket đã được tạo thành công</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Chúng tôi sẽ phản hồi sớm nhất có thể. Cảm ơn bạn đã liên hệ!
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => {
                setIsSubmitted(false);
                setShowConfirmation(true);
                setTitle(initialTitle);
                setDescription(initialDescription || reason || '');
              }}
              className="w-full max-w-sm"
            >
              Tạo ticket khác
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg flex items-center gap-2">
          Tạo Ticket Hỗ Trợ
        </CardTitle>
        <div className="flex items-center gap-3 text-sm text-muted-foreground">
          <span className="flex items-center gap-1">
            {getCategoryLabel(selectedCategory)}
          </span>
          <span>•</span>
          <span className={`font-medium ${getPriorityColor(selectedPriority)}`}>
            {selectedPriority}
          </span>
          {metadata?.autoGenerated && (
            <>
              <span>•</span>
              <span className="text-blue-600 flex items-center gap-1">Tự động tạo</span>
            </>
          )}
          {!metadata?.autoGenerated && !initialTitle && !reasonForEscalation && (
            <>
              <span>•</span>
              <span className="text-muted-foreground flex items-center gap-1">Thủ công</span>
            </>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category" className="text-sm font-medium">Loại ticket</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bug">Báo lỗi</SelectItem>
                  <SelectItem value="feature">Yêu cầu tính năng</SelectItem>
                  <SelectItem value="support">Hỗ trợ kỹ thuật</SelectItem>
                  <SelectItem value="feedback">Phản hồi</SelectItem>
                  <SelectItem value="complaint">Khiếu nại</SelectItem>
                  <SelectItem value="billing">Thanh toán</SelectItem>
                  <SelectItem value="account">Tài khoản</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="priority" className="text-sm font-medium">Độ ưu tiên</Label>
              <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Thấp</SelectItem>
                  <SelectItem value="medium">Trung bình</SelectItem>
                  <SelectItem value="high">Cao</SelectItem>
                  <SelectItem value="critical">Rất cao</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="title" className="text-sm font-medium">Tiêu đề *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Mô tả ngắn gọn vấn đề của bạn"
              required
              disabled={isSubmitting}
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="description" className="text-sm font-medium">Mô tả chi tiết *</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Mô tả chi tiết vấn đề, các bước tái hiện, hoặc yêu cầu của bạn"
              rows={4}
              required
              disabled={isSubmitting}
              className="mt-1"
              style={{ whiteSpace: 'pre-wrap' }}
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isSubmitting || !title.trim() || !description.trim()}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full size-4 border-b-2 border-white" />
                Đang gửi...
              </div>
            ) : (
              'Gửi Ticket'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
