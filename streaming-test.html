<!DOCTYPE html>
<html>
<head>
    <title>Streaming Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-area { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        #response { border: 1px solid #ddd; padding: 10px; min-height: 100px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🔬 AI SDK Streaming Test</h1>
    
    <div class="test-area">
        <h3>Raw Streaming Test</h3>
        <button onclick="testRawStreaming()">Test Raw Streaming</button>
        <div id="raw-status"></div>
        <div id="raw-response"></div>
    </div>
    
    <div class="test-area">
        <h3>AI SDK useChat Equivalent Test</h3>
        <button onclick="testAISDKStreaming()">Test AI SDK Style</button>
        <div id="sdk-status"></div>
        <div id="sdk-response"></div>
    </div>

    <script>
        async function testRawStreaming() {
            const statusEl = document.getElementById('raw-status');
            const responseEl = document.getElementById('raw-response');
            
            statusEl.innerHTML = '🔄 Testing raw streaming...';
            responseEl.innerHTML = '';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: crypto.randomUUID(),
                        message: {
                            id: crypto.randomUUID(),
                            createdAt: new Date().toISOString(),
                            role: 'user',
                            content: 'Say hello in Vietnamese',
                            parts: [{ type: 'text', text: 'Say hello in Vietnamese' }]
                        },
                        selectedChatModel: 'chat-model',
                        selectedVisibilityType: 'private'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }
                
                statusEl.innerHTML = '✅ Response received, reading stream...';
                
                const reader = response.body.getReader();
                let chunks = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        statusEl.innerHTML = `✅ Stream complete! ${chunks} chunks received`;
                        break;
                    }
                    
                    chunks++;
                    const chunk = new TextDecoder().decode(value);
                    responseEl.innerHTML += chunk;
                    
                    // Auto-scroll
                    responseEl.scrollTop = responseEl.scrollHeight;
                }
                
            } catch (error) {
                statusEl.innerHTML = `❌ Error: ${error.message}`;
                console.error('Raw streaming error:', error);
            }
        }
        
        async function testAISDKStreaming() {
            const statusEl = document.getElementById('sdk-status');
            const responseEl = document.getElementById('sdk-response');
            
            statusEl.innerHTML = '🔄 Testing AI SDK style streaming...';
            responseEl.innerHTML = '';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: crypto.randomUUID(),
                        message: {
                            id: crypto.randomUUID(),
                            createdAt: new Date().toISOString(),
                            role: 'user',
                            content: 'Count from 1 to 10 slowly',
                            parts: [{ type: 'text', text: 'Count from 1 to 10 slowly' }]
                        },
                        selectedChatModel: 'chat-model',
                        selectedVisibilityType: 'private'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }
                
                statusEl.innerHTML = '✅ Parsing data stream protocol...';
                
                const reader = response.body.getReader();
                let buffer = '';
                let messageText = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        statusEl.innerHTML = `✅ AI SDK streaming complete!`;
                        break;
                    }
                    
                    buffer += new TextDecoder().decode(value);
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || ''; // Keep incomplete line
                    
                    for (const line of lines) {
                        if (line.trim()) {
                            try {
                                // Parse AI SDK data stream format
                                if (line.startsWith('0:')) {
                                    // Text delta
                                    const textDelta = JSON.parse(line.substring(2));
                                    messageText += textDelta;
                                    responseEl.innerHTML = messageText;
                                } else if (line.startsWith('d:')) {
                                    // Final data
                                    statusEl.innerHTML = '✅ Message complete!';
                                } else if (line.startsWith('e:')) {
                                    // Error
                                    const error = JSON.parse(line.substring(2));
                                    statusEl.innerHTML = `❌ Stream error: ${JSON.stringify(error)}`;
                                }
                            } catch (parseError) {
                                console.log('Parse error for line:', line, parseError);
                            }
                        }
                    }
                    
                    // Auto-scroll
                    responseEl.scrollTop = responseEl.scrollHeight;
                }
                
            } catch (error) {
                statusEl.innerHTML = `❌ Error: ${error.message}`;
                console.error('AI SDK streaming error:', error);
            }
        }
    </script>
</body>
</html>
