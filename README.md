# CS Agent

AI-powered chatbot with PathRAG knowledge base integration.

## 🚀 Quick Start

### Development
```bash
# Run with script
./scripts/start.sh

# Or manual setup
pnpm install
pnpm dev
```

### Docker (Recommended for Production)
```bash
# Using docker-compose
cd docker && docker-compose up -d

# Or build manually
cd docker && ./docker-build.sh
```

## 📁 Project Structure

```
cs_agent/
├── docs/                    # Documentation
├── docker/                  # Docker configuration
├── scripts/                 # Build and utility scripts
├── tests/                   # Test files
├── logs/                    # Application logs
├── app/                     # Next.js app directory
├── components/              # React components
├── hooks/                   # React hooks
├── lib/                     # Shared libraries
├── pathrag/                 # PathRAG knowledge base service
├── public/                  # Static assets
└── [config files]           # package.json, tsconfig.json, etc.
```

## 🌐 Services

- **Web Interface**: http://localhost:3000
- **PathRAG API**: http://localhost:8123

## 📖 Documentation

- [Full Documentation](docs/README.md)
- [Docker Setup](docs/README-Docker.md)
- [Quick Docker Guide](docs/DOCKER-QUICKSTART.md)

## 🔧 Configuration

Copy `.env.example` to `.env` and configure your API keys and database settings.

## 📝 License

See [LICENSE](docs/LICENSE) for details.
