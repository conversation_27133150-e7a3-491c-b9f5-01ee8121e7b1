{"extends": ["next/core-web-vitals", "plugin:import/recommended", "plugin:import/typescript", "prettier", "plugin:tailwindcss/recommended"], "plugins": ["tailwindcss"], "rules": {"tailwindcss/no-custom-classname": "off", "tailwindcss/classnames-order": "off", "import/no-unresolved": "off", "react-hooks/exhaustive-deps": "warn", "tailwindcss/enforces-shorthand": "warn"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "ignorePatterns": ["**/components/ui/**", "**/lib/ai/models.test.ts"]}