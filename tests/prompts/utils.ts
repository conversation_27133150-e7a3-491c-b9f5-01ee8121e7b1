export function createTestPrompt(content: string, variables?: Record<string, any>): string {
  if (!variables) return content;
  
  return Object.entries(variables).reduce((prompt, [key, value]) => {
    return prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
  }, content);
}

export function validatePromptStructure(prompt: string): boolean {
  // Basic validation - check for common prompt structure
  const hasRole = prompt.includes('role') || prompt.includes('Role');
  const hasInstructions = prompt.length > 50;
  
  return hasRole && hasInstructions;
}

export const testPromptTemplates = {
  basic: "You are a helpful assistant. Please {{action}} the following: {{content}}",
  detailed: `
    Role: {{role}}
    Task: {{task}}
    Context: {{context}}
    
    Please provide a detailed response based on the above information.
  `,
  conversation: "Continue this conversation as {{character}}: {{previous_message}}"
};
