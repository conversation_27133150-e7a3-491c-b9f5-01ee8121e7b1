#!/usr/bin/env python3
"""
Document Retriever for PathRAG Knowledge Base
Handles document loading, processing, and similarity search
"""
import os
import json
import re
from typing import List, Dict, Any, Optional
from pathlib import Path

class DocumentRetriever:
    """
    Document retriever class for PathRAG knowledge base
    Supports text files, markdown, and basic document processing
    """
    
    def __init__(self, knowledgebase_path: str):
        """
        Initialize document retriever with knowledge base path
        
        Args:
            knowledgebase_path: Path to the knowledge base directory
        """
        self.knowledgebase_path = Path(knowledgebase_path)
        self.documents = []
        self.load_documents()
    
    def load_documents(self):
        """Load all documents from the knowledge base directory"""
        if not self.knowledgebase_path.exists():
            print(f"❌ Knowledge base path does not exist: {self.knowledgebase_path}")
            return
        
        supported_extensions = {'.txt', '.md', '.json', '.csv'}
        
        for file_path in self.knowledgebase_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                try:
                    content = self.load_file_content(file_path)
                    if content:
                        document = {
                            'filename': file_path.name,
                            'filepath': str(file_path),
                            'content': content,
                            'file_type': file_path.suffix.lower(),
                            'size': file_path.stat().st_size
                        }
                        self.documents.append(document)
                        print(f"✅ Loaded: {file_path.name} ({len(content)} chars)")
                except Exception as e:
                    print(f"❌ Error loading {file_path.name}: {e}")
        
        print(f"📚 Total documents loaded: {len(self.documents)}")
    
    def load_file_content(self, file_path: Path) -> Optional[str]:
        """
        Load content from a file with appropriate encoding
        
        Args:
            file_path: Path to the file to load
            
        Returns:
            File content as string or None if failed
        """
        try:
            # Try UTF-8 first
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                # Fallback to latin-1
                with open(file_path, 'r', encoding='latin-1') as f:
                    return f.read()
            except Exception as e:
                print(f"⚠️  Could not read {file_path.name}: {e}")
                return None
    
    def search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Search for documents matching the query
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            
        Returns:
            Dictionary with search results in PathRAG format
        """
        if not query.strip():
            return {
                'context_documents': [],
                'total_documents': 0,
                'query': query,
                'message': 'Empty query provided'
            }
        
        # Simple text-based search with relevance scoring
        results = []
        query_lower = query.lower()
        query_keywords = self.extract_keywords(query_lower)
        
        for doc in self.documents:
            content_lower = doc['content'].lower()
            relevance_score = self.calculate_relevance(content_lower, query_lower, query_keywords)
            
            if relevance_score > 0:
                results.append({
                    'content': doc['content'],
                    'filename': doc['filename'],
                    'file': doc['filename'],  # Alternative field name for compatibility
                    'relevance_score': round(relevance_score, 3),
                    'metadata': {
                        'filepath': doc['filepath'],
                        'file_type': doc['file_type'],
                        'size': doc['size']
                    }
                })
        
        # Sort by relevance score (descending)
        results.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        # Limit results
        limited_results = results[:max_results]
        
        return {
            'context_documents': limited_results,
            'total_documents': len(limited_results),
            'query': query,
            'keywords': query_keywords,
            'processing_type': 'context_only',
            'message': f'Found {len(limited_results)} relevant documents'
        }
    
    def extract_keywords(self, query: str) -> List[str]:
        """
        Extract keywords from query for better matching
        
        Args:
            query: Query string
            
        Returns:
            List of keywords
        """
        # Remove punctuation and split into words
        words = re.findall(r'\b\w+\b', query)
        
        # Filter out common stop words (Vietnamese and English)
        stop_words = {
            'là', 'của', 'và', 'có', 'được', 'này', 'đó', 'các', 'một', 'cho', 'với', 'từ', 'về',
            'the', 'is', 'at', 'which', 'on', 'and', 'a', 'to', 'as', 'are', 'was', 'for', 'with'
        }
        
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        return keywords
    
    def calculate_relevance(self, content: str, query: str, keywords: List[str]) -> float:
        """
        Calculate relevance score for a document
        
        Args:
            content: Document content (lowercase)
            query: Query string (lowercase)
            keywords: Extracted keywords
            
        Returns:
            Relevance score (0-1)
        """
        score = 0.0
        
        # Exact query match (highest weight)
        if query in content:
            score += 0.5
        
        # Keyword matching
        keyword_matches = 0
        for keyword in keywords:
            if keyword in content:
                keyword_matches += 1
                # More weight for exact word boundaries
                if re.search(r'\b' + re.escape(keyword) + r'\b', content):
                    score += 0.2
                else:
                    score += 0.1
        
        # Normalize keyword score
        if keywords:
            keyword_score = keyword_matches / len(keywords)
            score += keyword_score * 0.3
        
        # Bonus for multiple keyword matches
        if keyword_matches > 1:
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about loaded documents"""
        if not self.documents:
            return {'total_documents': 0, 'message': 'No documents loaded'}
        
        file_types = {}
        total_size = 0
        
        for doc in self.documents:
            file_type = doc['file_type']
            file_types[file_type] = file_types.get(file_type, 0) + 1
            total_size += doc['size']
        
        return {
            'total_documents': len(self.documents),
            'file_types': file_types,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'average_size_bytes': round(total_size / len(self.documents)) if self.documents else 0
        }


# Test function for debugging
if __name__ == "__main__":
    print("🧪 Testing DocumentRetriever...")
    
    # Test with current directory if knowledgebase doesn't exist
    test_path = "./knowledgebase"
    if not os.path.exists(test_path):
        test_path = "."
    
    retriever = DocumentRetriever(test_path)
    stats = retriever.get_document_stats()
    print(f"📊 Document Stats: {json.dumps(stats, indent=2)}")
    
    # Test search
    if retriever.documents:
        test_query = "test query"
        results = retriever.search(test_query, max_results=3)
        print(f"🔍 Search Results for '{test_query}': {json.dumps(results, indent=2, ensure_ascii=False)}")
    else:
        print("❌ No documents found to test search")
