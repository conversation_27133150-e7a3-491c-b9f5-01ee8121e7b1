#!/usr/bin/env python3
"""
PathRAG API Server - Clean version with context-only responses
Port 8123 with auto-kill existing processes
"""
import os
import sys
import signal
import subprocess
from flask import Flask, request, jsonify
from flask_cors import CORS
from document_retriever import DocumentRetriever

app = Flask(__name__)
CORS(app)

# Global document retriever
retriever = None
PORT = 8123

def kill_existing_processes():
    """Kill any existing processes on port 8123"""
    try:
        # Find processes using port 8123
        result = subprocess.run(['lsof', '-ti:8123'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    print(f"🔥 Killing existing process on port {PORT}: PID {pid}")
                    os.kill(int(pid), signal.SIGTERM)
                except (ProcessLookupError, ValueError):
                    pass
    except Exception as e:
        print(f"⚠️  Warning: Could not check/kill existing processes: {e}")

def init_retriever():
    """Initialize document retriever"""
    global retriever
    knowledgebase_path = './knowledgebase'
    
    if not os.path.exists(knowledgebase_path):
        print(f"❌ Error: Knowledgebase path not found: {knowledgebase_path}")
        sys.exit(1)
    
    retriever = DocumentRetriever(knowledgebase_path)
    print(f"✅ Document retriever initialized with {len(retriever.documents)} documents")

@app.route('/api/context', methods=['POST'])
def get_context():
    """Get context documents for a query - No LLM processing"""
    try:
        data = request.json
        query = data.get('query', '').strip()
        max_results = data.get('max_results', 5)
        
        if not query:
            return jsonify({
                'success': False,
                'message': 'Query is required',
                'data': None
            }), 400
        
        # Retrieve context documents (no LLM)
        result = retriever.search(query, max_results)
        
        # Format response
        response_data = {
            'query': result['query'],
            'keywords': result['keywords'],
            'context_documents': result['context_documents'],
            'total_documents': result['total_documents'],
            'message': f"Found {result['total_documents']} relevant documents",
            'processing_type': 'context_only'  # Indicate no LLM processing
        }
        
        return jsonify({
            'success': True,
            'message': 'Context retrieved successfully',
            'data': response_data
        })
        
    except Exception as e:
        print(f"❌ Error in get_context: {e}")
        return jsonify({
            'success': False,
            'message': f'Error retrieving context: {str(e)}',
            'data': None
        }), 500

@app.route('/api/documents', methods=['GET'])
def list_documents():
    """List all available documents in knowledgebase"""
    try:
        documents = []
        for filename, doc in retriever.documents.items():
            documents.append({
                'filename': filename,
                'size': len(doc['content']),
                'preview': doc['content'][:100] + "..." if len(doc['content']) > 100 else doc['content']
            })
        
        return jsonify({
            'success': True,
            'message': f'Found {len(documents)} documents',
            'data': {
                'total_documents': len(documents),
                'documents': documents
            }
        })
        
    except Exception as e:
        print(f"❌ Error in list_documents: {e}")
        return jsonify({
            'success': False,
            'message': f'Error listing documents: {str(e)}',
            'data': None
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'PathRAG Clean API',
        'version': '2.0.0',
        'port': PORT,
        'documents_loaded': len(retriever.documents) if retriever else 0,
        'processing_type': 'context_only',
        'llm_usage': False
    })

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get detailed server status"""
    return jsonify({
        'server': 'PathRAG Clean API',
        'version': '2.0.0',
        'port': PORT,
        'status': 'running',
        'features': {
            'llm_processing': False,
            'context_only': True,
            'auto_kill_existing': True,
            'vietnamese_support': True
        },
        'stats': {
            'total_documents': len(retriever.documents) if retriever else 0,
            'knowledgebase_path': './knowledgebase'
        }
    })

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print('\n🛑 Shutting down PathRAG API server...')
    sys.exit(0)

if __name__ == '__main__':
    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    print("🚀 Starting PathRAG Clean API Server...")
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Kill any existing processes on port 8123
    kill_existing_processes()
    
    # Initialize document retriever
    init_retriever()
    
    # Start server
    print(f"🌐 Server running on http://localhost:{PORT}")
    print("📚 Context-only API ready - No LLM processing, pure document retrieval")
    print("🔥 Auto-killed any existing processes on port 8123")
    print("\n📡 Available endpoints:")
    print(f"  POST /api/context     - Get context documents")
    print(f"  GET  /api/documents   - List all documents")
    print(f"  GET  /health          - Health check")
    print(f"  GET  /api/status      - Detailed status")
    print("\n💡 Press Ctrl+C to stop the server")
    
    try:
        app.run(host='0.0.0.0', port=PORT, debug=False)
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} still in use. Trying to force kill...")
            kill_existing_processes()
            print("🔄 Retrying in 2 seconds...")
            import time
            time.sleep(2)
            app.run(host='0.0.0.0', port=PORT, debug=False)
        else:
            raise e
