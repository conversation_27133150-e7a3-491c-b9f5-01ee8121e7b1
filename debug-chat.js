#!/usr/bin/env node

/**
 * Debug script cho Chat SDK - Kiểm tra API và streaming response
 */

const readline = require('readline');

async function testChatAPI() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('🔍 Chat SDK Debug Tool');
  console.log('========================\n');

  // Test 1: Kiểm tra API endpoint availability  
  console.log('📡 Test 1: Kiểm tra server availability...');
  let serverPort = 3001; // Default port khi 3000 bị occupied
  
  try {
    const response = await fetch(`http://localhost:${serverPort}`, {
      method: 'GET'
    });
    console.log(`✅ Server status: ${response.status} on port ${serverPort}`);
  } catch (error) {
    console.log(`❌ Server không accessible on port ${serverPort}: ${error.message}`);
    // Try port 3000 as fallback
    try {
      serverPort = 3000;
      const response = await fetch(`http://localhost:${serverPort}`, {
        method: 'GET'
      });
      console.log(`✅ Server status: ${response.status} on port ${serverPort}`);
    } catch (error2) {
      console.log(`❌ Server không accessible on port ${serverPort}: ${error2.message}`);
      return;
    }
  }

  // Test 2: Kiểm tra authentication
  console.log('\n🔐 Test 2: Kiểm tra authentication...');
  try {
    const authResponse = await fetch(`http://localhost:${serverPort}/api/auth/session`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const authData = await authResponse.text();
    console.log(`✅ Auth status: ${authResponse.status}`);
    console.log(`📄 Auth data: ${authData.slice(0, 100)}...`);
  } catch (error) {
    console.log(`❌ Auth check failed: ${error.message}`);
  }

  // Test 3: Test chat API với sample message
  const testMessage = await new Promise(resolve => {
    rl.question('\n💬 Nhập tin nhắn test (hoặc Enter để dùng default): ', resolve);
  });

  const message = testMessage || 'Hello, this is a test message';
  
  console.log('\n🚀 Test 3: Gửi message tới chat API...');
  
  try {
    const chatResponse = await fetch(`http://localhost:${serverPort}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: 'debug-chat-' + Date.now(),
        message: {
          id: 'msg-' + Date.now(),
          role: 'user',
          content: message,
          parts: [{ type: 'text', text: message }]
        },
        selectedChatModel: 'gemini-2.5-flash-preview-04-17',
        selectedVisibilityType: 'private'
      })
    });

    console.log(`📊 Response status: ${chatResponse.status}`);
    console.log(`📋 Response headers:`, Object.fromEntries(chatResponse.headers.entries()));

    if (chatResponse.status !== 200) {
      const errorText = await chatResponse.text();
      console.log(`❌ Error response: ${errorText}`);
      rl.close();
      return;
    }

    // Test streaming response
    console.log('\n📺 Đang đọc streaming response...\n');
    
    const reader = chatResponse.body?.getReader();
    if (!reader) {
      console.log('❌ Không có streaming body');
      rl.close();
      return;
    }

    let chunks = 0;
    let totalData = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        chunks++;
        const chunk = new TextDecoder().decode(value);
        totalData += chunk;
        
        console.log(`📦 Chunk ${chunks}: ${chunk.slice(0, 100)}${chunk.length > 100 ? '...' : ''}`);
        
        // Hiển thị progress
        process.stdout.write('.');
      }
    } catch (streamError) {
      console.log(`\n❌ Streaming error: ${streamError.message}`);
    }

    console.log(`\n\n✅ Streaming hoàn thành!`);
    console.log(`📊 Tổng chunks: ${chunks}`);
    console.log(`📏 Tổng data length: ${totalData.length}`);
    console.log(`📄 Sample data: ${totalData.slice(0, 200)}...`);

  } catch (error) {
    console.log(`❌ Chat API error: ${error.message}`);
    console.log(`🔍 Error stack: ${error.stack}`);
  }

  rl.close();
}

// Chạy test
testChatAPI().catch(console.error);
