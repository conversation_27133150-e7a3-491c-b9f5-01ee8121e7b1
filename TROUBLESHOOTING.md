# Chat SDK Troubleshooting Guide 🔧

## Vấn đề: Terminal báo 200 nhưng giao diện không hiển thị tin nhắn

### Cá<PERSON> nguyên nhân phổ biến:

## 1. 🌐 Frontend Streaming Issues

### Kiểm tra Browser Console:
```bash
# Mở Developer Tools (F12) và kiểm tra:
# - Console tab: Tìm JavaScript errors
# - Network tab: Xem request/response của /api/chat
```

### Các lỗi thường gặp:
- `ERR_NETWORK` - Network connection issue
- `CORS error` - Cross-origin resource sharing issue  
- `Streaming abort` - Streaming bị ngắt giữa chừng
- `JSON parse error` - Response format không đúng

## 2. 🔧 AI SDK Streaming Problems

### Kiểm tra AI SDK version:
```bash
cd /Users/<USER>/DotB/cs_agent
npm list @ai-sdk/react ai
```

### Common fixes:
```bash
# Update AI SDK nếu cần
npm update @ai-sdk/react ai

# Clear Next.js cache
rm -rf .next
npm run build
```

## 3. 🔐 Authentication Issues

### Kiểm tra session:
- Guest session có được tạo không?
- NextAuth configuration đúng chưa?

```bash
# Test auth endpoint
curl http://localhost:3000/api/auth/session
```

## 4. 🗄️ Database Connection

### Kiểm tra messages có được save không:
```bash
# Check database logs
npm run db:studio
# Hoặc check migration status
npm run db:push
```

## 5. 🐛 Debugging Steps

### Bước 1: Chạy debug script
```bash
node debug-chat.js
```

### Bước 2: Kiểm tra Network tab
1. Mở DevTools (F12)
2. Vào Network tab
3. Gửi tin nhắn
4. Xem request `/api/chat`:
   - Status code: Có phải 200?
   - Response type: Có phải streaming?
   - Response body: Có data không?

### Bước 3: Kiểm tra Console errors
```javascript
// Thêm debug log vào Chat component
console.log('Messages state:', messages);
console.log('Chat status:', status);
console.log('useChat data:', data);
```

### Bước 4: Test với curl
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-123",
    "message": {
      "id": "msg-123", 
      "role": "user",
      "content": "Hello",
      "parts": [{"type": "text", "text": "Hello"}]
    },
    "selectedChatModel": "gemini-2.5-flash-preview-04-17",
    "selectedVisibilityType": "private"
  }'
```

## 6. 🚀 Quick Fixes

### Fix 1: Restart Development Server
```bash
# Terminal 1: Dừng current server (Ctrl+C)
npm run dev

# Terminal 2: Clear cache
rm -rf .next node_modules/.cache
```

### Fix 2: Check Environment Variables
```bash
# Kiểm tra .env file
cat .env | grep -E "(NEXTAUTH|AI|DATABASE)"
```

### Fix 3: Update Dependencies
```bash
# Update critical packages
npm update ai @ai-sdk/react @ai-sdk/google next
```

## 7. 📊 Monitoring & Logs

### Server logs để theo dõi:
```bash
# Trong terminal chạy dev server, tìm:
- ✓ POST /api/chat 200
- 💾 Saving assistant message: {...}
- Any error logs
```

### Client logs để theo dõi:
```javascript
// Trong browser console:
// useChat hook status changes
// Message array updates  
// Streaming data flow
```

## 8. 🔄 Known Issues & Workarounds

### Issue: DataStreamHandler logic bị remove
**Fix**: Restore hoặc implement custom stream handling

### Issue: Next.js 15 + React 19 RC compatibility
**Fix**: Downgrade hoặc update theo breaking changes

### Issue: AI SDK throttling
**Fix**: Adjust `experimental_throttle` value trong useChat

## 9. 🆘 Emergency Debugging

Nếu vẫn không resolve được, thêm extensive logging:

```typescript
// Trong chat/route.ts - thêm vào onFinish
onFinish: async ({ response }) => {
  console.log('🔍 onFinish called with:', {
    responseMessages: response.messages,
    messageCount: response.messages.length,
    lastMessage: response.messages[response.messages.length - 1]
  });
  
  // ... existing code
}
```

```typescript  
// Trong Chat component - thêm useEffect
useEffect(() => {
  console.log('🔍 Messages updated:', {
    count: messages.length,
    lastMessage: messages[messages.length - 1],
    status,
    isLoading: status === 'streaming'
  });
}, [messages, status]);
```

---

**Liên hệ:** Nếu vấn đề vẫn persist, check GitHub issues hoặc AI SDK documentation.
