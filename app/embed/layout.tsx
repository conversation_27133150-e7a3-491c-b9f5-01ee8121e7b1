import { Toaster } from 'sonner';
import type { Metada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';

import '@/app/globals.css';

export const metadata: Metadata = {
  title: 'Chat SDK - Embed Mode',
  description: 'AI Chatbot Template for Embedding',
  robots: 'noindex, nofollow', // Prevent indexing of embed pages
};

const geist = Geist({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist',
});

const geistMono = Geist_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist-mono',
});

export default function EmbedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${geist.variable} ${geistMono.variable}`}
    >
      <head>
        <script
          // biome-ignore lint/security/noDangerouslySetInnerHtml: Required for iframe communication
          dangerouslySetInnerHTML={{
            __html: `
              // Iframe communication helper
              window.isIframe = window.self !== window.top;
              window.iframeOrigin = document.referrer ? new URL(document.referrer).origin : null;
            `,
          }}
        />
      </head>
      <body className="antialiased h-screen overflow-hidden" suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <Toaster position="top-center" />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
