import { type NextRequest, NextResponse } from 'next/server';
import { createOrGetExternalUser } from '@/lib/db/queries';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, source } = body;

    // DEBUG LOGGING
    console.log('[IFRAME AUTH] body:', body);

    // Simple user_id + source authentication
    if (user_id && source) {
      try {
        const userData = {
          externalId: user_id,
          source: source as any,
          metadata: {},
        };

        const user = await createOrGetExternalUser(userData);

        return NextResponse.json({
          success: true,
          user: {
            id: user.id,
            externalId: user_id,
            source: source,
          },
        });
      } catch (error) {
        console.error('User creation failed:', error);
        return NextResponse.json(
          { error: 'Failed to create user session' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Missing required authentication parameters' },
      { status: 400 }
    );
  } catch (error) {
    console.error('iframe auth error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
