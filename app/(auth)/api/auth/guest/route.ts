import { signIn } from '@/app/(auth)/auth';
import { isDevelopmentEnvironment } from '@/lib/constants';
import { getToken } from 'next-auth/jwt';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const redirectUrl = searchParams.get('redirectUrl') || '/';

  console.log('🔄 Guest auth GET request:', {
    redirectUrl,
    headers: Object.fromEntries(request.headers.entries())
  });

  const token = await getToken({
    req: request,
    secret: process.env.AUTH_SECRET,
    secureCookie: !isDevelopmentEnvironment,
  });

  if (token) {
    console.log('✅ Already has token, redirecting to home');
    return NextResponse.redirect(new URL('/', request.url));
  }

  console.log('🔄 Creating guest session...');
  return signIn('guest', { redirect: true, redirectTo: redirectUrl });
}

export async function POST(request: Request) {
  // Handle POST requests for guest authentication
  // This is needed when useChat makes POST requests that get redirected here
  const { searchParams } = new URL(request.url);
  const redirectUrl = searchParams.get('redirectUrl') || '/';

  const token = await getToken({
    req: request,
    secret: process.env.AUTH_SECRET,
    secureCookie: !isDevelopmentEnvironment,
  });

  if (token) {
    // If already authenticated, redirect to the original URL
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // For POST requests, we need to sign in and then redirect
  // But since signIn expects form data, we'll create a guest session and redirect
  try {
    const signInResult = await signIn('guest', { 
      redirect: false,
      redirectTo: redirectUrl 
    });
    
    if (signInResult?.url) {
      return NextResponse.redirect(new URL(signInResult.url, request.url));
    } else {
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
  } catch (error) {
    console.error('Guest sign-in failed:', error);
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }
}
