import { compare } from 'bcrypt-ts';
import NextAuth, { type DefaultSession, type NextAuthConfig } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { createGuestUser, getUser, createOrGetExternalUser } from '@/lib/db/queries';
import { authConfig } from './auth.config';
import { DUMMY_PASSWORD } from '@/lib/constants';
import type { DefaultJWT } from 'next-auth/jwt';

export type UserType = 'guest' | 'regular' | 'external';

declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string;
      type: UserType;
      externalId?: string;
      source?: string;
    } & DefaultSession['user'];
  }

  interface User {
    id: string;
    email?: string | null;
    type: UserType;
    externalId?: string;
    source?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    id: string;
    type: UserType;
    externalId?: string;
    source?: string;
  }
}

export const authOptions = {
  ...authConfig,
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        const users = await getUser(email);

        if (users.length === 0) {
          await compare(password, DUMMY_PASSWORD);
          return null;
        }

        const [user] = users;

        if (!user.password) {
          await compare(password, DUMMY_PASSWORD);
          return null;
        }

        const passwordsMatch = await compare(password, user.password);

        if (!passwordsMatch) return null;

        return { 
          id: user.id,
          email: user.email,
          type: 'regular' as UserType,
          externalId: user.externalId || undefined,
          source: user.source || undefined
        };
      },
    }),
    Credentials({
      id: 'guest',
      credentials: {},
      async authorize() {
        const [guestUser] = await createGuestUser();
        return { 
          id: guestUser.id,
          email: guestUser.email,
          type: 'guest' as UserType,
          externalId: guestUser.externalId || undefined,
          source: guestUser.source || undefined
        };
      },
    }),

  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.type = user.type;
        if (user.externalId) token.externalId = user.externalId;
        if (user.source) token.source = user.source;
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.type = token.type;
        if (token.externalId) session.user.externalId = token.externalId;
        if (token.source) session.user.source = token.source;
      }

      return session;
    },
  },
} satisfies NextAuthConfig;

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth(authOptions);
