'use client';

import React from 'react';

type ErrorProps = {
  error: Error;
  reset: () => void;
};

export default function GlobalError({ error, reset }: ErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <h1 className="text-3xl font-bold"><PERSON><PERSON> có lỗi xảy ra</h1>
      <p className="mt-2 text-red-600">{error.message}</p>
      <button
        onClick={() => reset()}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Thử lại
      </button>
    </div>
  );
} 