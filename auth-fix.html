<!DOCTYPE html>
<html>
<head>
    <title>Chat SDK - Auth Fix</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        button { background: #0070f3; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0051a0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Chat SDK Auth Fix</h1>
        <p>Tool để fix vấn đề authentication và test chat functionality.</p>
        
        <div id="status"></div>
        
        <button onclick="checkAuthStatus()">🔍 Check Auth Status</button>
        <button onclick="createGuestSession()">👤 Create Guest Session</button>
        <button onclick="testChatAPI()">💬 Test Chat API</button>
        <button onclick="goToChat()">➡️ Go to Chat</button>
        
        <div id="output"></div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            document.getElementById('status').innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function addOutput(content) {
            const output = document.getElementById('output');
            output.innerHTML += `<pre>${content}</pre>`;
        }
        
        async function checkAuthStatus() {
            updateStatus('🔍 Checking authentication status...', 'info');
            addOutput('=== AUTH STATUS CHECK ===');
            
            try {
                // Check cookies
                addOutput(`Cookies: ${document.cookie || 'No cookies found'}`);
                
                // Check session
                const sessionResponse = await fetch('/api/auth/session');
                const sessionData = await sessionResponse.json();
                addOutput(`Session: ${JSON.stringify(sessionData, null, 2)}`);
                
                if (sessionData.user) {
                    updateStatus(`✅ Authenticated as ${sessionData.user.email} (${sessionData.user.type})`, 'success');
                } else {
                    updateStatus('❌ Not authenticated', 'error');
                }
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                addOutput(`Error: ${error.message}`);
            }
        }
        
        async function createGuestSession() {
            updateStatus('👤 Creating guest session...', 'info');
            addOutput('=== CREATING GUEST SESSION ===');
            
            try {
                const response = await fetch('/api/auth/guest');
                addOutput(`Guest auth response: ${response.status} ${response.statusText}`);
                
                if (response.ok || response.status === 307) {
                    updateStatus('✅ Guest session created successfully!', 'success');
                    // Wait a bit then check session
                    setTimeout(checkAuthStatus, 1000);
                } else {
                    updateStatus(`❌ Failed to create guest session: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                addOutput(`Error: ${error.message}`);
            }
        }
        
        async function testChatAPI() {
            updateStatus('💬 Testing chat API...', 'info');
            addOutput('=== TESTING CHAT API ===');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: crypto.randomUUID(),
                        message: {
                            id: crypto.randomUUID(),
                            createdAt: new Date().toISOString(),
                            role: 'user',
                            content: 'Hello from auth fix tool!',
                            parts: [{ type: 'text', text: 'Hello from auth fix tool!' }]
                        },
                        selectedChatModel: 'chat-model',
                        selectedVisibilityType: 'private'
                    })
                });
                
                addOutput(`Chat API response: ${response.status} ${response.statusText}`);
                addOutput(`Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                
                if (response.ok) {
                    updateStatus('🎉 Chat API working! Streaming response received.', 'success');
                    addOutput('✅ Chat API is working properly!');
                } else {
                    const errorText = await response.text();
                    updateStatus(`❌ Chat API error: ${response.status}`, 'error');
                    addOutput(`Error response: ${errorText}`);
                }
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                addOutput(`Error: ${error.message}`);
            }
        }
        
        function goToChat() {
            window.location.href = '/';
        }
        
        // Auto-check on load
        window.onload = checkAuthStatus;
    </script>
</body>
</html>
